# ============================================================================
# TELEGRAM PROXY CONFIGURATION TEMPLATE
# ============================================================================
# Copy this file to .env and update with your actual values

# Flask Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# ============================================================================
# MESSAGE SENDING STRATEGY
# ============================================================================
# PRIMARY_METHOD: Chọn phương thức chính để gửi message
# - "bot_api": Sử dụng Bot API làm chính, MTProto làm fallback (Khuyến nghị)
# - "mtproto": Sử dụng MTProto làm chính, Bot API làm fallback (Hiệu suất cao)
PRIMARY_METHOD=bot_api

# ============================================================================
# BOT API CONFIGURATION
# ============================================================================
# Bot API luôn được bật để làm fallback
# Không cần cấu hình gì thêm - sử dụng bot_token từ API request

# ============================================================================
# MTPROTO CONFIGURATION (Telegram API Credentials)
# ============================================================================
# Lấy từ https://my.telegram.org/apps
MTPROTO_ENABLED=True
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_NAME=telegram_proxy_session
MTPROTO_TEST_MODE=False

# ============================================================================
# MTPROXY CONFIGURATION (Your Self-hosted MTProxy Server)
# ============================================================================
# Cài đặt MTProxy server: docker run -d -p 443:443 telegrammessenger/proxy
# Lấy secret từ: docker logs <container_name>
MTPROXY_ENABLED=True
MTPROXY_HOST=your_mtproxy_host_or_ip
MTPROXY_PORT=443
MTPROXY_SECRET=your_mtproxy_secret_from_docker_logs

# ============================================================================
# LOGGING & PERFORMANCE
# ============================================================================
LOG_LEVEL=INFO
REQUEST_TIMEOUT=30

# ============================================================================
# QUICK START EXAMPLES
# ============================================================================
#
# 🚀 QUICK START - Bot API Only:
#    1. Set: PRIMARY_METHOD=bot_api
#    2. Set: MTPROTO_ENABLED=False
#    3. Run: python main.py
#    4. Use your bot_token in API requests
#
# 🔥 RECOMMENDED - Bot API + MTProto Fallback:
#    1. Setup MTProxy: docker run -d -p 443:443 telegrammessenger/proxy
#    2. Get API credentials from https://my.telegram.org/apps
#    3. Update TELEGRAM_API_ID, TELEGRAM_API_HASH
#    4. Update MTPROXY_HOST, MTPROXY_SECRET
#    5. Set: PRIMARY_METHOD=bot_api
#    6. Set: MTPROTO_ENABLED=True, MTPROXY_ENABLED=True
#    7. Run: python main.py
#
# ⚡ HIGH PERFORMANCE - MTProto Primary:
#    1. Complete setup above
#    2. Set: PRIMARY_METHOD=mtproto
#    3. Run: python main.py (will ask for phone verification first time)
#
# ============================================================================
