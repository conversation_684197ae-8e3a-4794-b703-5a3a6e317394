# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# MTProto Configuration (Telegram API)
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_NAME=telegram_proxy_session

# MTProto Connection Settings
MTPROTO_ENABLED=True
MTPROTO_SERVER_HOST=**************
MTPROTO_SERVER_PORT=443
MTPROTO_TEST_MODE=False

# Logging Configuration
LOG_LEVEL=INFO

# Request Configuration
REQUEST_TIMEOUT=30
