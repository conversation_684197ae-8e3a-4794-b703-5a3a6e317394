# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# MTProto Configuration (Telegram API)
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_NAME=telegram_proxy_session

# MTProxy Configuration (Your self-hosted MTProxy)
MTPROXY_ENABLED=True
MTPROXY_HOST=your_mtproxy_host
MTPROXY_PORT=443
MTPROXY_SECRET=your_mtproxy_secret

# MTProto Connection Settings
MTPROTO_ENABLED=True
MTPROTO_TEST_MODE=False

# Message Sending Strategy
# PRIMARY_METHOD: "bot_api" hoặc "mtproto"
PRIMARY_METHOD=bot_api

# Logging Configuration
LOG_LEVEL=INFO

# Request Configuration
REQUEST_TIMEOUT=30
