# Cách hoạt động của Telegram Proxy

## Tổng quan

Ứng dụng này hỗ trợ **2 phương thức** gửi message đến Telegram:

1. **Bot API** - Sử dụng bot_token (giữ nguyên logic cũ)
2. **MTProto** - Sử dụng user account qua MTProxy tự cài đặt

## Luồng hoạt động

```
API Request → Primary Method → Success? → Return
                    ↓
                 Fallback Method → Success? → Return
                    ↓
                 Return Error
```

## Chi tiết từng phương thức

### 1. Bot API Mode (PRIMARY_METHOD=bot_api)

**Đ<PERSON>y là phương thức mặc định, giữ nguyên logic cũ:**

```
Request với bot_token → Bot API (HTTPS) → Telegram Server
                           ↓ (nếu fail)
                      MTProto via MTProxy → Telegram Server
```

**Đặc điểm:**
- ✅ <PERSON><PERSON> dụng `bot_token` như cũ
- ✅ <PERSON>ết nối HTTPS trực tiếp đến api.telegram.org
- ✅ Không cần authentication user
- ✅ Fallback tự động sang MTProto nếu Bot API bị chặn

### 2. MTProto Mode (PRIMARY_METHOD=mtproto)

**Phương thức mới, sử dụng MTProxy:**

```
Request với bot_token → MTProto via MTProxy → Telegram Server
                           ↓ (nếu fail)
                      Bot API (HTTPS) → Telegram Server
```

**Đặc điểm:**
- 🔄 Sử dụng MTProto protocol qua MTProxy của bạn
- 🔐 Cần authentication user account một lần
- 🚀 Hiệu suất cao, ít bị chặn
- ✅ Fallback sang Bot API nếu MTProto fail

## Cấu hình

### Bot API Only (Đơn giản nhất)
```bash
PRIMARY_METHOD=bot_api
MTPROTO_ENABLED=False
```

### Bot API + MTProto Fallback (Khuyến nghị)
```bash
PRIMARY_METHOD=bot_api
MTPROTO_ENABLED=True
MTPROXY_ENABLED=True
MTPROXY_HOST=your_server
MTPROXY_SECRET=your_secret
```

### MTProto + Bot API Fallback (Hiệu suất cao)
```bash
PRIMARY_METHOD=mtproto
MTPROTO_ENABLED=True
MTPROXY_ENABLED=True
MTPROXY_HOST=your_server
MTPROXY_SECRET=your_secret
```

## API Request Format

**Giữ nguyên format cũ:**

```json
{
    "chat_id": "*********",
    "text_message": "Hello World!",
    "bot_token": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",
    "parse_mode": "HTML"
}
```

## Response Format

### Thành công
```json
{
    "status": "Message sent successfully via Bot API"
}
```

```json
{
    "status": "Message sent via MTProto+MTProxy: Message sent successfully via MTProto"
}
```

### Fallback
```json
{
    "status": "Message sent via MTProto+MTProxy fallback: Message sent successfully via MTProto"
}
```

### Lỗi
```json
{
    "error": "Missing required fields"
}
```

## Logs

### Bot API Mode
```
INFO - Sending message via Bot API
INFO - Message sent to chat_id ********* via Bot API
```

### MTProto Mode
```
INFO - Sending message via MTProto+MTProxy (primary)
INFO - Message sent via MTProto to chat_id *********
```

### Fallback
```
WARNING - Bot API failed, trying MTProto+MTProxy fallback...
INFO - Message sent via MTProto to chat_id *********
```

## Ưu điểm từng phương thức

### Bot API
- ✅ Đơn giản, không cần setup phức tạp
- ✅ Hoạt động với mọi bot token
- ✅ Không cần authentication user
- ❌ Có thể bị chặn ở một số quốc gia
- ❌ Rate limit nghiêm ngặt hơn

### MTProto via MTProxy
- ✅ Hiệu suất cao, ít bị chặn
- ✅ Sử dụng proxy server riêng
- ✅ Rate limit linh hoạt hơn
- ✅ Kết nối ổn định hơn
- ❌ Cần setup MTProxy server
- ❌ Cần authentication user account

## Khi nào sử dụng gì?

### Sử dụng Bot API làm primary khi:
- Bạn chỉ có bot token
- Không muốn setup phức tạp
- Kết nối internet ổn định
- Không bị chặn Telegram

### Sử dụng MTProto làm primary khi:
- Có MTProxy server riêng
- Cần hiệu suất cao
- Bị chặn Bot API
- Có user account để authenticate

## Troubleshooting

### Bot API fails
1. Kiểm tra bot_token có đúng không
2. Kiểm tra chat_id có đúng không
3. Kiểm tra kết nối internet
4. Kiểm tra có bị chặn api.telegram.org không

### MTProto fails
1. Kiểm tra MTProxy server có chạy không
2. Kiểm tra MTPROXY_SECRET có đúng không
3. Kiểm tra session file có bị lỗi không
4. Thử xóa session và đăng nhập lại

### Cả hai đều fail
1. Kiểm tra cấu hình .env
2. Kiểm tra logs chi tiết
3. Test từng phương thức riêng biệt
4. Kiểm tra firewall/network
