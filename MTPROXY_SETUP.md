# Hướng dẫn cài đặt MTProxy Server

## Tổng quan

MTProxy là proxy server ch<PERSON>h thức của Telegram gi<PERSON><PERSON> bypass các hạn chế mạng và cải thiện kết nối đến Telegram.

## Cách 1: <PERSON><PERSON> dụng Docker (Khuyến nghị)

### Bước 1: Cài đặt Docker
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install docker.io

# CentOS/RHEL
sudo yum install docker

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker
```

### Bước 2: Chạy MTProxy
```bash
# Tạo và chạy MTProxy container
docker run -d --name mtproxy \
  -p 443:443 \
  -p 8080:8080 \
  --restart=unless-stopped \
  telegrammessenger/proxy:latest

# Kiểm tra logs để lấy thông tin proxy
docker logs mtproxy
```

### Bước 3: Lấy thông tin proxy
```bash
# Xem logs để lấy secret và link
docker logs mtproxy

# Output sẽ có dạng:
# [+] Using secret: 17b8c78d4b0b4e0c8b5a2f3d4e5f6a7b
# [+] Proxy is ready! Use this link: https://t.me/proxy?server=YOUR_IP&port=443&secret=17b8c78d4b0b4e0c8b5a2f3d4e5f6a7b
```

## Cách 2: Cài đặt thủ công

### Bước 1: Cài đặt dependencies
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install git curl build-essential libssl-dev zlib1g-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install git curl openssl-devel zlib-devel
```

### Bước 2: Clone và build MTProxy
```bash
git clone https://github.com/TelegramMessenger/MTProxy.git
cd MTProxy
make

# Tạo thư mục cho binary
mkdir -p objs/bin
cd objs/bin
```

### Bước 3: Tải cấu hình Telegram
```bash
# Tải secret key và config
curl -s https://core.telegram.org/getProxySecret -o proxy-secret
curl -s https://core.telegram.org/getProxyConfig -o proxy-multi.conf
```

### Bước 4: Tạo secret key
```bash
# Tạo secret key ngẫu nhiên
head -c 16 /dev/urandom | xxd -ps
```

### Bước 5: Chạy MTProxy
```bash
# Chạy MTProxy (thay <SECRET> bằng secret key của bạn)
./mtproto-proxy -u nobody -p 8888 -H 443 -S <SECRET> --aes-pwd proxy-secret proxy-multi.conf -M 1

# Ví dụ:
./mtproto-proxy -u nobody -p 8888 -H 443 -S 17b8c78d4b0b4e0c8b5a2f3d4e5f6a7b --aes-pwd proxy-secret proxy-multi.conf -M 1
```

## Cách 3: Sử dụng script tự động

### MTProxy Easy Install Script
```bash
# Tải và chạy script cài đặt tự động
curl -L -o mtp_install.sh https://git.io/fj5ru && bash mtp_install.sh
```

## Cấu hình Firewall

### UFW (Ubuntu)
```bash
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp
sudo ufw reload
```

### Firewalld (CentOS/RHEL)
```bash
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### iptables
```bash
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
sudo iptables-save > /etc/iptables/rules.v4
```

## Kiểm tra MTProxy

### Kiểm tra port
```bash
# Kiểm tra port đang listen
netstat -tlnp | grep :443
ss -tlnp | grep :443
```

### Test kết nối
```bash
# Test từ local
telnet localhost 443

# Test từ remote
telnet YOUR_SERVER_IP 443
```

## Cấu hình cho ứng dụng

Sau khi cài đặt MTProxy thành công, cập nhật file `.env`:

```bash
# MTProxy Configuration
MTPROXY_ENABLED=True
MTPROXY_HOST=your_server_ip
MTPROXY_PORT=443
MTPROXY_SECRET=your_secret_key
```

## Troubleshooting

### Lỗi "Permission denied"
```bash
# Chạy với sudo hoặc thay đổi port
sudo ./mtproto-proxy ...
# hoặc sử dụng port > 1024
```

### Lỗi "Address already in use"
```bash
# Kiểm tra process đang sử dụng port
sudo lsof -i :443
sudo kill -9 <PID>
```

### Lỗi kết nối
```bash
# Kiểm tra firewall
sudo ufw status
sudo iptables -L

# Kiểm tra logs
docker logs mtproxy
journalctl -u mtproxy
```

## Monitoring

### Kiểm tra status
```bash
# Docker
docker ps | grep mtproxy
docker stats mtproxy

# Process
ps aux | grep mtproto-proxy
```

### Logs
```bash
# Docker logs
docker logs -f mtproxy

# System logs
tail -f /var/log/mtproxy.log
```

## Bảo mật

1. **Thay đổi secret key định kỳ**
2. **Sử dụng firewall để hạn chế truy cập**
3. **Monitor logs để phát hiện abuse**
4. **Cập nhật MTProxy thường xuyên**

## Performance Tuning

### Tăng file descriptor limit
```bash
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf
```

### Tối ưu kernel parameters
```bash
echo "net.core.rmem_max = 134217728" >> /etc/sysctl.conf
echo "net.core.wmem_max = 134217728" >> /etc/sysctl.conf
sysctl -p
```
