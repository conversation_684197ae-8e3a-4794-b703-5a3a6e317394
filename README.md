# Telegram Proxy

A Flask-based proxy service for forwarding messages to Telegram using MTProto protocol with fallback to Bot API.

## Features

- Forward messages using MTProto (Telegram's native protocol)
- Automatic fallback to Bot API if MTProto fails
- Environment-based configuration
- Configurable logging levels
- Request timeout configuration
- Support for both user accounts and bot tokens

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Copy the environment template:
```bash
cp .env.example .env
```

3. Get Telegram API credentials:
   - Go to https://my.telegram.org/apps
   - Create a new application
   - Get your `api_id` and `api_hash`

4. Configure your settings in `.env`:
```bash
# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# MTProto Configuration (Telegram API)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_NAME=telegram_proxy_session

# MTProxy Configuration (Your self-hosted MTProxy)
MTPROXY_ENABLED=True
MTPROXY_HOST=your_mtproxy_host
MTPROXY_PORT=443
MTPROXY_SECRET=your_mtproxy_secret

# MTProto Connection Settings
MTPROTO_ENABLED=True
MTPROTO_TEST_MODE=False

# Logging Configuration
LOG_LEVEL=INFO

# Request Configuration
REQUEST_TIMEOUT=30
```

## Usage

1. Start the server:
```bash
python main.py
```

2. Send a POST request to `/forward` with the following JSON payload:
```json
{
    "chat_id": "your_chat_id",
    "text_message": "Your message text",
    "bot_token": "your_bot_token",
    "parse_mode": "HTML"
}
```

## MTProxy Configuration

To use your self-hosted MTProxy:

1. Set `MTPROXY_ENABLED=True` in your `.env` file
2. Configure your MTProxy server details:
   - `MTPROXY_HOST`: Your MTProxy server hostname/IP
   - `MTPROXY_PORT`: Your MTProxy server port (usually 443)
   - `MTPROXY_SECRET`: Your MTProxy secret key
3. Configure your Telegram API credentials (`TELEGRAM_API_ID` and `TELEGRAM_API_HASH`)
4. The service will connect to Telegram through your MTProxy

**Important Notes:**
- MTProxy helps bypass restrictions and improve connection stability
- You need to set up your own MTProxy server first
- MTProto uses your user account, not bot tokens
- You need to authenticate once (the session will be saved)
- Bot API is used as fallback for reliability

**Setting up MTProxy:**
- Follow official guide: https://github.com/TelegramMessenger/MTProxy
- Or use Docker: `docker run -d -p 443:443 telegrammessenger/proxy`

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_HOST` | Flask server host | `0.0.0.0` |
| `FLASK_PORT` | Flask server port | `8188` |
| `FLASK_DEBUG` | Enable Flask debug mode | `False` |
| `TELEGRAM_API_ID` | Telegram API ID | `0` |
| `TELEGRAM_API_HASH` | Telegram API Hash | `` |
| `TELEGRAM_SESSION_NAME` | Session file name | `telegram_proxy_session` |
| `MTPROXY_ENABLED` | Enable MTProxy | `False` |
| `MTPROXY_HOST` | MTProxy server host | `` |
| `MTPROXY_PORT` | MTProxy server port | `443` |
| `MTPROXY_SECRET` | MTProxy secret key | `` |
| `MTPROTO_ENABLED` | Enable MTProto | `False` |
| `MTPROTO_TEST_MODE` | Use test servers | `False` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `REQUEST_TIMEOUT` | Request timeout in seconds | `30` |

## API Endpoints

### POST /forward

Forwards a message to Telegram Bot API.

**Request Body:**
```json
{
    "chat_id": "string|number",
    "text_message": "string",
    "bot_token": "string",
    "parse_mode": "string (optional, default: HTML)"
}
```

**Response:**
- `200 OK`: Message forwarded successfully
- `400 Bad Request`: Missing required fields
- `500 Internal Server Error`: Request failed
