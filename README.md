# Telegram Proxy

A Flask-based proxy service for forwarding messages to Telegram Bot API with MTProxy support.

## Features

- Forward messages to Telegram Bot API
- MTProxy support for bypassing restrictions
- Environment-based configuration
- Configurable logging levels
- Request timeout configuration

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Copy the environment template:
```bash
cp .env.example .env
```

3. Configure your settings in `.env`:
```bash
# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# MTProxy Configuration
MTPROXY_ENABLED=True
MTPROXY_HOST=your_mtproxy_host
MTPROXY_PORT=your_mtproxy_port
MTPROXY_SECRET=your_mtproxy_secret
MTPROXY_TYPE=socks5

# Logging Configuration
LOG_LEVEL=INFO

# Request Configuration
REQUEST_TIMEOUT=30
```

## Usage

1. Start the server:
```bash
python main.py
```

2. Send a POST request to `/forward` with the following JSON payload:
```json
{
    "chat_id": "your_chat_id",
    "text_message": "Your message text",
    "bot_token": "your_bot_token",
    "parse_mode": "HTML"
}
```

## MTProxy Configuration

To use MTProxy:

1. Set `MTPROXY_ENABLED=True` in your `.env` file
2. Configure your MTProxy host, port, and type
3. The service will automatically route Telegram API requests through the proxy

Supported proxy types:
- `socks5` (default)
- `socks4`
- `http`

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_HOST` | Flask server host | `0.0.0.0` |
| `FLASK_PORT` | Flask server port | `8188` |
| `FLASK_DEBUG` | Enable Flask debug mode | `False` |
| `MTPROXY_ENABLED` | Enable MTProxy | `False` |
| `MTPROXY_HOST` | MTProxy host | `127.0.0.1` |
| `MTPROXY_PORT` | MTProxy port | `1080` |
| `MTPROXY_SECRET` | MTProxy secret | `` |
| `MTPROXY_TYPE` | Proxy type | `socks5` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `REQUEST_TIMEOUT` | Request timeout in seconds | `30` |

## API Endpoints

### POST /forward

Forwards a message to Telegram Bot API.

**Request Body:**
```json
{
    "chat_id": "string|number",
    "text_message": "string",
    "bot_token": "string",
    "parse_mode": "string (optional, default: HTML)"
}
```

**Response:**
- `200 OK`: Message forwarded successfully
- `400 Bad Request`: Missing required fields
- `500 Internal Server Error`: Request failed
