# Telegram Proxy

A Flask-based proxy service for forwarding messages to Telegram using MTProto protocol with fallback to Bot API.

## Features

- Forward messages using MTProto (Telegram's native protocol)
- Automatic fallback to Bot API if MTProto fails
- Environment-based configuration
- Configurable logging levels
- Request timeout configuration
- Support for both user accounts and bot tokens

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Copy the environment template:
```bash
cp .env.example .env
```

3. Get Telegram API credentials:
   - Go to https://my.telegram.org/apps
   - Create a new application
   - Get your `api_id` and `api_hash`

4. Configure your settings in `.env`:
```bash
# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# MTProto Configuration (Telegram API)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_NAME=telegram_proxy_session

# MTProto Connection Settings
MTPROTO_ENABLED=True
MTPROTO_SERVER_HOST=**************
MTPROTO_SERVER_PORT=443
MTPROTO_TEST_MODE=False

# Logging Configuration
LOG_LEVEL=INFO

# Request Configuration
REQUEST_TIMEOUT=30
```

## Usage

1. Start the server:
```bash
python main.py
```

2. Send a POST request to `/forward` with the following JSON payload:
```json
{
    "chat_id": "your_chat_id",
    "text_message": "Your message text",
    "bot_token": "your_bot_token",
    "parse_mode": "HTML"
}
```

## MTProto Configuration

To use MTProto:

1. Set `MTPROTO_ENABLED=True` in your `.env` file
2. Configure your Telegram API credentials (`TELEGRAM_API_ID` and `TELEGRAM_API_HASH`)
3. The service will automatically use MTProto for sending messages
4. If MTProto fails, it will automatically fallback to Bot API

**Important Notes:**
- MTProto uses your user account, not bot tokens
- You need to authenticate once (the session will be saved)
- MTProto provides better performance and bypasses some restrictions
- Bot API is used as fallback for reliability

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_HOST` | Flask server host | `0.0.0.0` |
| `FLASK_PORT` | Flask server port | `8188` |
| `FLASK_DEBUG` | Enable Flask debug mode | `False` |
| `TELEGRAM_API_ID` | Telegram API ID | `0` |
| `TELEGRAM_API_HASH` | Telegram API Hash | `` |
| `TELEGRAM_SESSION_NAME` | Session file name | `telegram_proxy_session` |
| `MTPROTO_ENABLED` | Enable MTProto | `False` |
| `MTPROTO_SERVER_HOST` | MTProto server host | `**************` |
| `MTPROTO_SERVER_PORT` | MTProto server port | `443` |
| `MTPROTO_TEST_MODE` | Use test servers | `False` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `REQUEST_TIMEOUT` | Request timeout in seconds | `30` |

## API Endpoints

### POST /forward

Forwards a message to Telegram Bot API.

**Request Body:**
```json
{
    "chat_id": "string|number",
    "text_message": "string",
    "bot_token": "string",
    "parse_mode": "string (optional, default: HTML)"
}
```

**Response:**
- `200 OK`: Message forwarded successfully
- `400 Bad Request`: Missing required fields
- `500 Internal Server Error`: Request failed
