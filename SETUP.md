# Hướng dẫn Setup MTProto Telegram Proxy

## Bước 1: Cài đặt dependencies

```bash
pip install -r requirements.txt
```

## Bước 2: L<PERSON>y Telegram API credentials

1. T<PERSON>y cập https://my.telegram.org/apps
2. Đăng nhập bằng số điện thoại Telegram của bạn
3. Tạo một ứng dụng mới:
   - **App title**: Telegram Proxy
   - **Short name**: telegram-proxy
   - **Platform**: Desktop
4. Lưu lại `api_id` và `api_hash`

## Bước 3: Cấu hình environment

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```bash
# MTProto Configuration (Telegram API)
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here
TELEGRAM_SESSION_NAME=telegram_proxy_session

# MTProto Connection Settings
MTPROTO_ENABLED=True
```

## Bước 4: Ch<PERSON><PERSON> ứng dụng

```bash
python main.py
```

**Lần đầu chạy**: Bạn sẽ được yêu cầu nhập số điện thoại và mã xác thực để đăng nhập vào Telegram.

## Bước 5: Test API

```bash
curl -X POST http://localhost:8188/forward \
  -H "Content-Type: application/json" \
  -d '{
    "chat_id": "your_chat_id",
    "text_message": "Hello from MTProto!",
    "bot_token": "your_bot_token",
    "parse_mode": "HTML"
  }'
```

## Lưu ý quan trọng

1. **MTProto vs Bot API**:
   - MTProto sử dụng tài khoản user của bạn
   - Bot API sử dụng bot token
   - Ứng dụng sẽ ưu tiên MTProto, fallback về Bot API nếu cần

2. **Session file**:
   - File session sẽ được tạo sau lần đăng nhập đầu tiên
   - Không chia sẻ file session với người khác
   - Thêm `*.session` vào `.gitignore`

3. **Rate limiting**:
   - MTProto có rate limit khác với Bot API
   - Ứng dụng tự động xử lý FloodWait

4. **Bảo mật**:
   - Không commit file `.env` vào git
   - Bảo vệ API credentials của bạn
   - Sử dụng HTTPS trong production

## Troubleshooting

### Lỗi "MTProto client not initialized"
- Kiểm tra `TELEGRAM_API_ID` và `TELEGRAM_API_HASH` trong `.env`
- Đảm bảo `MTPROTO_ENABLED=True`

### Lỗi authentication
- Xóa file session và đăng nhập lại
- Kiểm tra số điện thoại và mã xác thực

### Fallback to Bot API
- Đây là hành vi bình thường khi MTProto gặp lỗi
- Kiểm tra logs để xem lý do cụ thể
