# ============================================================================
# BOT API ONLY CONFIGURATION
# ============================================================================
# Cấu hình đơn giản nhất - chỉ sử dụng Bot API

# Flask Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# Message Sending Strategy
PRIMARY_METHOD=bot_api

# MTProto Configuration - DISABLED
MTPROTO_ENABLED=False
TELEGRAM_API_ID=0
TELEGRAM_API_HASH=
TELEGRAM_SESSION_NAME=telegram_proxy_session
MTPROTO_TEST_MODE=False

# MTProxy Configuration - DISABLED
MTPROXY_ENABLED=False
MTPROXY_HOST=
MTPROXY_PORT=443
MTPROXY_SECRET=

# Logging & Performance
LOG_LEVEL=INFO
REQUEST_TIMEOUT=30

# ============================================================================
# USAGE:
# 1. Copy this file to .env
# 2. Run: python main.py
# 3. Send requests with bot_token
# ============================================================================
