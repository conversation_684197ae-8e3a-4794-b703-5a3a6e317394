# ============================================================================
# MTPROTO PRIMARY CONFIGURATION (HIGH PERFORMANCE)
# ============================================================================
# MTProto qua MTProxy làm chính, Bot API làm fallback

# Flask Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# Message Sending Strategy
PRIMARY_METHOD=mtproto

# MTProto Configuration - ENABLED as primary
MTPROTO_ENABLED=True
TELEGRAM_API_ID=your_api_id_from_my_telegram_org
TELEGRAM_API_HASH=your_api_hash_from_my_telegram_org
TELEGRAM_SESSION_NAME=telegram_proxy_session
MTPROTO_TEST_MODE=False

# MTProxy Configuration - ENABLED
MTPROXY_ENABLED=True
MTPROXY_HOST=your_mtproxy_server_ip
MTPROXY_PORT=443
MTPROXY_SECRET=your_mtproxy_secret_from_docker_logs

# Logging & Performance
LOG_LEVEL=INFO
REQUEST_TIMEOUT=30

# ============================================================================
# SETUP STEPS:
# 1. Setup MTProxy: docker run -d -p 443:443 telegrammessenger/proxy
# 2. Get secret: docker logs <container_name>
# 3. Get API credentials from https://my.telegram.org/apps
# 4. Update TELEGRAM_API_ID, TELEGRAM_API_HASH
# 5. Update MTPROXY_HOST, MTPROXY_SECRET
# 6. Copy this file to .env
# 7. Run: python main.py
# 8. Authenticate with phone number (first time only)
# 
# BENEFITS:
# - Faster message delivery
# - Better bypass capabilities
# - More stable connection
# - Higher rate limits
# ============================================================================
