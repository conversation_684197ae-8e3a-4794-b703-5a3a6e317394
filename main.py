import logging
import os
from flask import Flask, request, jsonify
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Cấu hình logging
log_level = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
logging.basicConfig(level=log_level)

# MTProxy Configuration
MTPROXY_ENABLED = os.getenv('MTPROXY_ENABLED', 'False').lower() == 'true'
MTPROXY_HOST = os.getenv('MTPROXY_HOST', '127.0.0.1')
MTPROXY_PORT = int(os.getenv('MTPROXY_PORT', '1080'))
MTPROXY_SECRET = os.getenv('MTPROXY_SECRET', '')
MTPROXY_TYPE = os.getenv('MTPROXY_TYPE', 'socks5')

# Flask Configuration
FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
FLASK_PORT = int(os.getenv('FLASK_PORT', '8188'))
FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

# Request Configuration
REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))

def get_proxy_config():
    """Tạo cấu hình proxy cho requests"""
    if not MTPROXY_ENABLED:
        return None

    proxy_url = f"{MTPROXY_TYPE}://{MTPROXY_HOST}:{MTPROXY_PORT}"

    return {
        'http': proxy_url,
        'https': proxy_url
    }

def get_session_with_proxy():
    """Tạo session với cấu hình proxy"""
    session = requests.Session()

    proxy_config = get_proxy_config()
    if proxy_config:
        session.proxies.update(proxy_config)
        logging.info(f"Using MTProxy: {MTPROXY_TYPE}://{MTPROXY_HOST}:{MTPROXY_PORT}")
    else:
        logging.info("MTProxy disabled, using direct connection")

    return session

# Tạo Flask app
app = Flask(__name__)

@app.route('/forward', methods=['POST'])
def forward_message():
    data = request.get_json()

    # Log toàn bộ request body
    logging.info(f"Received request with body: {data}")

    chat_id = data.get('chat_id')
    text_message = data.get('text_message')
    bot_token = data.get('bot_token')
    parse_mode = data.get('parse_mode', 'HTML')

    if not chat_id or not text_message or not bot_token:
        return jsonify({'error': 'Missing required fields'}), 400

    # Đảm bảo chat_id là chuỗi
    chat_id = str(chat_id)

    # Log thông tin chat_id và bot_token để kiểm tra
    logging.info(f"Chat ID: {chat_id}, Bot Token: {bot_token}")

    telegram_api_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

    payload = {
        'chat_id': chat_id,
        'text': text_message,
        'parse_mode': parse_mode
    }

    try:
        # Gửi yêu cầu HTTP POST đến Telegram API
        response = requests.post(telegram_api_url, json=payload)

        # Kiểm tra phản hồi từ Telegram
        if response.status_code == 200:
            logging.info(f"Forwarded message to chat_id {chat_id}")
            return jsonify({'status': 'Message forwarded successfully'}), 200
        else:
            logging.error(f"Error forwarding message: {response.text}")
            return jsonify({'error': response.text}), response.status_code
    except requests.RequestException as e:
        logging.error(f"Error forwarding message: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8188)
