#!/usr/bin/env python3
"""
Quick setup script for Telegram Proxy
"""

import os
import shutil
import subprocess
import sys

def print_header(text):
    print("\n" + "=" * 60)
    print(f" {text}")
    print("=" * 60)

def print_step(step, text):
    print(f"\n{step}. {text}")

def ask_choice(question, choices):
    print(f"\n{question}")
    for i, choice in enumerate(choices, 1):
        print(f"  {i}. {choice}")
    
    while True:
        try:
            choice = int(input("\nChọn (1-{}): ".format(len(choices))))
            if 1 <= choice <= len(choices):
                return choice - 1
            else:
                print("Lựa chọn không hợp lệ!")
        except ValueError:
            print("Vui lòng nhập số!")

def ask_input(question, default=""):
    if default:
        response = input(f"{question} [{default}]: ").strip()
        return response if response else default
    else:
        while True:
            response = input(f"{question}: ").strip()
            if response:
                return response
            print("<PERSON>hông được để trống!")

def check_docker():
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def setup_mtproxy():
    print_step("MTProxy Setup", "Cài đặt MTProxy server")
    
    if not check_docker():
        print("❌ Docker không được cài đặt!")
        print("Vui lòng cài Docker trước: https://docs.docker.com/get-docker/")
        return None, None
    
    print("🐳 Đang cài đặt MTProxy với Docker...")
    
    try:
        # Run MTProxy container
        cmd = [
            "docker", "run", "-d", 
            "--name", "mtproxy-telegram",
            "-p", "443:443",
            "--restart=unless-stopped",
            "telegrammessenger/proxy:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Lỗi chạy MTProxy: {result.stderr}")
            return None, None
        
        print("✅ MTProxy container đã được tạo!")
        
        # Get logs to extract secret
        import time
        print("⏳ Đang lấy thông tin proxy...")
        time.sleep(3)
        
        logs_result = subprocess.run(
            ["docker", "logs", "mtproxy-telegram"], 
            capture_output=True, text=True
        )
        
        # Extract secret from logs
        secret = None
        for line in logs_result.stdout.split('\n'):
            if 'secret' in line.lower() and len(line) > 20:
                # Try to extract hex string
                import re
                hex_match = re.search(r'[a-fA-F0-9]{32,}', line)
                if hex_match:
                    secret = hex_match.group()
                    break
        
        if not secret:
            print("⚠️  Không thể tự động lấy secret. Vui lòng chạy:")
            print("   docker logs mtproxy-telegram")
            secret = ask_input("Nhập MTProxy secret")
        
        # Get server IP
        try:
            import requests
            ip = requests.get('https://api.ipify.org', timeout=5).text.strip()
            print(f"🌐 Server IP: {ip}")
        except:
            ip = ask_input("Nhập IP server của bạn")
        
        return ip, secret
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return None, None

def create_env_file(config_type, mtproxy_host=None, mtproxy_secret=None):
    print_step("Environment Setup", "Tạo file .env")
    
    # Copy template
    template_file = f"env-examples/{config_type}.env"
    if os.path.exists(template_file):
        shutil.copy(template_file, ".env")
        print(f"✅ Đã copy template từ {template_file}")
    else:
        shutil.copy(".env.example", ".env")
        print("✅ Đã copy từ .env.example")
    
    # Update with user input
    if config_type != "bot-api-only":
        api_id = ask_input("Nhập TELEGRAM_API_ID (từ my.telegram.org)")
        api_hash = ask_input("Nhập TELEGRAM_API_HASH (từ my.telegram.org)")
        
        if mtproxy_host and mtproxy_secret:
            host = mtproxy_host
            secret = mtproxy_secret
        else:
            host = ask_input("Nhập MTPROXY_HOST")
            secret = ask_input("Nhập MTPROXY_SECRET")
        
        # Update .env file
        with open(".env", "r") as f:
            content = f.read()
        
        content = content.replace("your_api_id", api_id)
        content = content.replace("your_api_hash", api_hash)
        content = content.replace("your_mtproxy_host", host)
        content = content.replace("your_mtproxy_secret", secret)
        
        with open(".env", "w") as f:
            f.write(content)
        
        print("✅ Đã cập nhật file .env")

def install_dependencies():
    print_step("Dependencies", "Cài đặt Python packages")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Đã cài đặt dependencies")
        return True
    except subprocess.CalledProcessError:
        print("❌ Lỗi cài đặt dependencies")
        return False

def main():
    print_header("🚀 TELEGRAM PROXY SETUP")
    
    # Choose configuration type
    config_types = [
        "Bot API Only (Đơn giản nhất)",
        "Bot API + MTProto Fallback (Khuyến nghị)", 
        "MTProto Primary (Hiệu suất cao)"
    ]
    
    config_files = [
        "bot-api-only",
        "bot-api-with-fallback", 
        "mtproto-primary"
    ]
    
    choice = ask_choice("Chọn cấu hình:", config_types)
    config_type = config_files[choice]
    
    print(f"\n✅ Đã chọn: {config_types[choice]}")
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Setup MTProxy if needed
    mtproxy_host = None
    mtproxy_secret = None
    
    if config_type != "bot-api-only":
        setup_choice = ask_choice(
            "MTProxy setup:", 
            ["Tôi đã có MTProxy server", "Cài đặt MTProxy mới với Docker"]
        )
        
        if setup_choice == 1:  # Setup new
            mtproxy_host, mtproxy_secret = setup_mtproxy()
            if not mtproxy_host:
                print("❌ Không thể setup MTProxy. Thoát.")
                return
    
    # Create .env file
    create_env_file(config_type, mtproxy_host, mtproxy_secret)
    
    # Final instructions
    print_header("🎉 SETUP HOÀN THÀNH!")
    
    print("\n📋 Các bước tiếp theo:")
    print("1. Chạy server: python main.py")
    
    if config_type != "bot-api-only":
        print("2. Lần đầu chạy sẽ yêu cầu xác thực số điện thoại")
        print("3. Sau đó có thể sử dụng bình thường")
    
    print("\n🧪 Test API:")
    print("   python test_api.py")
    
    print("\n📚 Tài liệu:")
    print("   - README.md: Hướng dẫn chi tiết")
    print("   - HOW_IT_WORKS.md: Cách hoạt động")
    print("   - MTPROXY_SETUP.md: Setup MTProxy")

if __name__ == "__main__":
    main()
