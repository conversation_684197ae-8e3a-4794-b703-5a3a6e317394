#!/usr/bin/env python3
"""
Script test API với cả Bot API và MTProto
"""

import requests
import json
import time

# Cấu hình test
API_URL = "http://localhost:8188/forward"
TEST_CHAT_ID = "your_chat_id"  # Thay bằng chat_id thực
TEST_BOT_TOKEN = "your_bot_token"  # Thay bằng bot_token thực

def test_api_call(message_text, description=""):
    """Test một API call"""
    print(f"\n🧪 {description}")
    print("-" * 50)
    
    payload = {
        "chat_id": TEST_CHAT_ID,
        "text_message": message_text,
        "bot_token": TEST_BOT_TOKEN,
        "parse_mode": "HTML"
    }
    
    try:
        print(f"📤 Gửi: {message_text}")
        
        start_time = time.time()
        response = requests.post(API_URL, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"⏱️  Thời gian: {end_time - start_time:.2f}s")
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Thành công: {result.get('status', 'Unknown')}")
            return True
        else:
            print(f"❌ Lỗi: {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def main():
    """Hàm chính"""
    print("🚀 TELEGRAM PROXY API TEST")
    print("=" * 60)
    
    # Kiểm tra cấu hình
    if TEST_CHAT_ID == "your_chat_id" or TEST_BOT_TOKEN == "your_bot_token":
        print("❌ Vui lòng cấu hình TEST_CHAT_ID và TEST_BOT_TOKEN trong file này")
        return
    
    # Test cases
    tests = [
        ("🧪 Test message #1 - Bot API", "Test cơ bản"),
        ("🔄 Test message #2 - Fallback", "Test fallback mechanism"),
        ("<b>Test HTML</b> formatting", "Test HTML parsing"),
        ("Test với emoji 🎉🚀💻", "Test emoji support"),
        ("Test message dài " + "x" * 100, "Test message dài"),
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for message, description in tests:
        if test_api_call(message, description):
            success_count += 1
        time.sleep(2)  # Tránh rate limit
    
    # Kết quả
    print("\n" + "=" * 60)
    print(f"📊 KẾT QUẢ TEST: {success_count}/{total_tests} thành công")
    
    if success_count == total_tests:
        print("🎉 TẤT CẢ TEST THÀNH CÔNG!")
    elif success_count > 0:
        print("⚠️  MỘT SỐ TEST THẤT BẠI")
    else:
        print("❌ TẤT CẢ TEST THẤT BẠI")
        print("\n💡 Kiểm tra:")
        print("   - Server có đang chạy không? (python main.py)")
        print("   - Chat ID và Bot Token có đúng không?")
        print("   - Cấu hình .env có đúng không?")

if __name__ == "__main__":
    main()
