#!/usr/bin/env python3
"""
Script test kết nối MTProxy và MTProto
"""

import os
import asyncio
import logging
from pyrogram import Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Cấu hình logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# MTProto Configuration
TELEGRAM_API_ID = int(os.getenv('TELEGRAM_API_ID', '0'))
TELEGRAM_API_HASH = os.getenv('TELEGRAM_API_HASH', '')
TELEGRAM_SESSION_NAME = os.getenv('TELEGRAM_SESSION_NAME', 'test_session')
MTPROTO_TEST_MODE = os.getenv('MTPROTO_TEST_MODE', 'False').lower() == 'true'

# MTProxy Configuration
MTPROXY_ENABLED = os.getenv('MTPROXY_ENABLED', 'False').lower() == 'true'
MTPROXY_HOST = os.getenv('MTPROXY_HOST', '')
MTPROXY_PORT = int(os.getenv('MTPROXY_PORT', '443'))
MTPROXY_SECRET = os.getenv('MTPROXY_SECRET', '')

def test_configuration():
    """Kiểm tra cấu hình"""
    print("=== KIỂM TRA CẤU HÌNH ===")
    
    # Kiểm tra API credentials
    if not TELEGRAM_API_ID or not TELEGRAM_API_HASH:
        print("❌ Thiếu TELEGRAM_API_ID hoặc TELEGRAM_API_HASH")
        return False
    else:
        print(f"✅ API ID: {TELEGRAM_API_ID}")
        print(f"✅ API Hash: {TELEGRAM_API_HASH[:8]}...")
    
    # Kiểm tra MTProxy config
    if MTPROXY_ENABLED:
        if not MTPROXY_HOST or not MTPROXY_SECRET:
            print("❌ MTProxy enabled nhưng thiếu HOST hoặc SECRET")
            return False
        else:
            print(f"✅ MTProxy Host: {MTPROXY_HOST}")
            print(f"✅ MTProxy Port: {MTPROXY_PORT}")
            print(f"✅ MTProxy Secret: {MTPROXY_SECRET[:8]}...")
    else:
        print("ℹ️  MTProxy disabled")
    
    return True

async def test_mtproto_connection():
    """Test kết nối MTProto"""
    print("\n=== TEST KẾT NỐI MTPROTO ===")
    
    try:
        # Cấu hình proxy
        proxy_config = None
        if MTPROXY_ENABLED and MTPROXY_HOST and MTPROXY_SECRET:
            proxy_config = {
                "scheme": "mtproto",
                "hostname": MTPROXY_HOST,
                "port": MTPROXY_PORT,
                "secret": MTPROXY_SECRET
            }
            print(f"🔗 Sử dụng MTProxy: {MTPROXY_HOST}:{MTPROXY_PORT}")
        else:
            print("🔗 Kết nối trực tiếp (không qua proxy)")
        
        # Tạo client
        client = Client(
            name=TELEGRAM_SESSION_NAME,
            api_id=TELEGRAM_API_ID,
            api_hash=TELEGRAM_API_HASH,
            test_mode=MTPROTO_TEST_MODE,
            proxy=proxy_config,
            no_updates=True
        )
        
        print("🔄 Đang kết nối...")
        
        # Kết nối và test
        async with client:
            me = await client.get_me()
            print(f"✅ Kết nối thành công!")
            print(f"👤 User: {me.first_name} (@{me.username})")
            print(f"📱 Phone: {me.phone_number}")
            
            # Test gửi message đến chính mình
            try:
                await client.send_message("me", "🧪 Test message từ MTProxy script")
                print("✅ Gửi test message thành công!")
            except Exception as e:
                print(f"⚠️  Không thể gửi test message: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi kết nối: {e}")
        return False

def test_mtproxy_connectivity():
    """Test kết nối đến MTProxy server"""
    if not MTPROXY_ENABLED or not MTPROXY_HOST:
        print("\n=== MTProxy không được cấu hình ===")
        return True
    
    print(f"\n=== TEST KẾT NỐI MTPROXY SERVER ===")
    
    import socket
    
    try:
        # Test TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((MTPROXY_HOST, MTPROXY_PORT))
        sock.close()
        
        if result == 0:
            print(f"✅ MTProxy server {MTPROXY_HOST}:{MTPROXY_PORT} có thể kết nối")
            return True
        else:
            print(f"❌ Không thể kết nối đến MTProxy server {MTPROXY_HOST}:{MTPROXY_PORT}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi test kết nối MTProxy: {e}")
        return False

async def main():
    """Hàm chính"""
    print("🧪 TELEGRAM MTPROXY + MTPROTO TEST SCRIPT")
    print("=" * 50)
    
    # Test 1: Kiểm tra cấu hình
    if not test_configuration():
        print("\n❌ Cấu hình không hợp lệ. Vui lòng kiểm tra file .env")
        return
    
    # Test 2: Test kết nối MTProxy server
    if not test_mtproxy_connectivity():
        print("\n⚠️  MTProxy server không khả dụng")
        print("💡 Kiểm tra:")
        print("   - MTProxy server có đang chạy không?")
        print("   - Firewall có chặn port không?")
        print("   - Host và port có đúng không?")
    
    # Test 3: Test kết nối MTProto
    success = await test_mtproto_connection()
    
    if success:
        print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
        print("✅ Cấu hình MTProxy + MTProto hoạt động tốt")
    else:
        print("\n❌ CÓ LỖI XẢY RA")
        print("💡 Kiểm tra:")
        print("   - API ID và API Hash có đúng không?")
        print("   - MTProxy secret có đúng không?")
        print("   - Đã đăng nhập Telegram chưa?")

if __name__ == "__main__":
    asyncio.run(main())
